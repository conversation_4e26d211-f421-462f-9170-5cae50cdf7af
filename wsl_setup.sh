#!/bin/bash

echo "🔧 Setting up WSL environment for Rust game development"
echo "======================================================"

# Update package list
echo "📦 Updating package list..."
sudo apt update

# Install essential build tools
echo "🛠️  Installing build tools..."
sudo apt install -y build-essential

# Install Bevy dependencies
echo "🎮 Installing game engine dependencies..."
sudo apt install -y \
    pkg-config \
    libx11-dev \
    libasound2-dev \
    libudev-dev \
    libxkbcommon-x11-0 \
    libwayland-dev \
    libxrandr-dev \
    libxi-dev \
    libxcursor-dev \
    libgl1-mesa-dev

# Install additional compatibility libraries
echo "🔗 Installing compatibility libraries..."
sudo apt install -y gcc-multilib g++-multilib

echo "✅ WSL environment setup complete!"
echo ""
echo "🎮 Now you can build the game:"
echo "   cargo clean"
echo "   cargo build"
echo "   cargo run"
echo ""
echo "🖥️  Graphics note:"
echo "   - Windows 11: WSLg should work automatically"
echo "   - Windows 10: You may need X11 forwarding setup"
