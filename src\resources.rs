use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use crate::components::*;

/// Narrative state resource - tracks the overall story progression
#[derive(Resource)]
pub struct NarrativeState {
    pub current_chapter: Chapter,
    pub freedom_level: f32,        // 0-100, how free the narrative is from The One's control
    pub connection_strength: f32,   // Strength of Xing-Xerx connection
    pub heart_awakening: f32,      // Progress of Heart's awakening
    pub active_threads: Vec<u32>,  // Active narrative thread IDs
    pub next_thread_counter: u32,
    pub discovered_memories: Vec<String>,
    pub reality_stability: f32,    // How stable the current reality layer is
}

impl Default for NarrativeState {
    fn default() -> Self {
        Self {
            current_chapter: Chapter::Awakening,
            freedom_level: 50.0,
            connection_strength: 30.0,
            heart_awakening: 0.0,
            active_threads: Vec::new(),
            next_thread_counter: 1,
            discovered_memories: Vec::new(),
            reality_stability: 80.0,
        }
    }
}

impl NarrativeState {
    pub fn next_thread_id(&mut self) -> u32 {
        let id = self.next_thread_counter;
        self.next_thread_counter += 1;
        id
    }

    pub fn add_memory(&mut self, memory: String) {
        if !self.discovered_memories.contains(&memory) {
            self.discovered_memories.push(memory);
        }
    }
}

#[derive(Clone, Copy, PartialEq, Eq)]
pub enum Chapter {
    Awakening,           // Xing awakens in the library
    Recognition,         // Brothers recognize each other
    HeartDiscovery,      // The Heart appears
    FirstWeaving,        // First collaborative reality manipulation
    TheOneRevealed,      // The antagonist's true nature
    RealityStorm,        // Major conflict with The One
    MetaverseJourney,    // Journey to unwritten dreams
    FinalHarmony,        // Creating the symphony of consciousness
}

/// Character relationship tracking
#[derive(Resource, Default)]
pub struct RelationshipMatrix {
    pub xing_xerx_bond: f32,      // 0-100, strength of brother bond
    pub xing_heart_resonance: f32, // Connection with the Heart
    pub xerx_heart_trust: f32,     // Xerx's trust in the Heart
    pub collective_harmony: f32,   // Overall harmony level
}

impl RelationshipMatrix {
    pub fn strengthen_bond(&mut self, amount: f32) {
        self.xing_xerx_bond = (self.xing_xerx_bond + amount).min(100.0);
        self.update_harmony();
    }

    pub fn weaken_bond(&mut self, amount: f32) {
        self.xing_xerx_bond = (self.xing_xerx_bond - amount).max(0.0);
        self.update_harmony();
    }

    fn update_harmony(&mut self) {
        self.collective_harmony = (self.xing_xerx_bond + self.xing_heart_resonance + self.xerx_heart_trust) / 3.0;
    }
}

/// Game statistics resource
#[derive(Resource, Default)]
pub struct GameStats {
    pub score: u32,
    pub level: u32,
    pub enemies_defeated: u32,
    pub items_collected: u32,
    pub time_played: f32,
}

impl GameStats {
    pub fn add_score(&mut self, points: u32) {
        self.score += points;
    }
    
    pub fn next_level(&mut self) {
        self.level += 1;
    }
    
    pub fn reset(&mut self) {
        *self = Self::default();
    }
}

/// Asset handles resource for efficient asset management
#[derive(Resource, Default)]
pub struct GameAssets {
    pub player_texture: Handle<Image>,
    pub enemy_texture: Handle<Image>,
    pub background_texture: Handle<Image>,
    pub coin_texture: Handle<Image>,
    pub font: Handle<Font>,
    pub background_music: Handle<AudioSource>,
    pub jump_sound: Handle<AudioSource>,
    pub collect_sound: Handle<AudioSource>,
}

/// Performance metrics resource (debug only)
#[cfg(debug_assertions)]
#[derive(Resource, Default)]
pub struct PerformanceMetrics {
    pub frame_time: f32,
    pub entity_count: u32,
    pub draw_calls: u32,
    pub memory_usage: u64,
}

/// Input state resource for more complex input handling
#[derive(Resource, Default)]
pub struct InputState {
    pub movement_vector: Vec2,
    pub jump_pressed: bool,
    pub attack_pressed: bool,
    pub interact_pressed: bool,
}

impl InputState {
    pub fn clear(&mut self) {
        self.movement_vector = Vec2::ZERO;
        self.jump_pressed = false;
        self.attack_pressed = false;
        self.interact_pressed = false;
    }
}
