use bevy::prelude::*;
use serde::{Deserialize, Serialize};

/// Global game settings resource
#[derive(Resource, Serialize, Deserialize)]
pub struct GameSettings {
    pub player_speed: f32,
    pub enemy_speed: f32,
    pub volume: f32,
    pub difficulty: Difficulty,
    pub graphics_quality: GraphicsQuality,
}

impl Default for GameSettings {
    fn default() -> Self {
        Self {
            player_speed: 200.0,
            enemy_speed: 100.0,
            volume: 0.8,
            difficulty: Difficulty::Normal,
            graphics_quality: GraphicsQuality::High,
        }
    }
}

#[derive(Serialize, Deserialize, Clone, Copy)]
pub enum Difficulty {
    Easy,
    Normal,
    Hard,
}

#[derive(Serialize, Deserialize, Clone, Copy)]
pub enum GraphicsQuality {
    Low,
    Medium,
    High,
    Ultra,
}

/// Game statistics resource
#[derive(Resource, Default)]
pub struct GameStats {
    pub score: u32,
    pub level: u32,
    pub enemies_defeated: u32,
    pub items_collected: u32,
    pub time_played: f32,
}

impl GameStats {
    pub fn add_score(&mut self, points: u32) {
        self.score += points;
    }
    
    pub fn next_level(&mut self) {
        self.level += 1;
    }
    
    pub fn reset(&mut self) {
        *self = Self::default();
    }
}

/// Asset handles resource for efficient asset management
#[derive(Resource, Default)]
pub struct GameAssets {
    pub player_texture: Handle<Image>,
    pub enemy_texture: Handle<Image>,
    pub background_texture: Handle<Image>,
    pub coin_texture: Handle<Image>,
    pub font: Handle<Font>,
    pub background_music: Handle<AudioSource>,
    pub jump_sound: Handle<AudioSource>,
    pub collect_sound: Handle<AudioSource>,
}

/// Performance metrics resource (debug only)
#[cfg(debug_assertions)]
#[derive(Resource, Default)]
pub struct PerformanceMetrics {
    pub frame_time: f32,
    pub entity_count: u32,
    pub draw_calls: u32,
    pub memory_usage: u64,
}

/// Input state resource for more complex input handling
#[derive(Resource, Default)]
pub struct InputState {
    pub movement_vector: Vec2,
    pub jump_pressed: bool,
    pub attack_pressed: bool,
    pub interact_pressed: bool,
}

impl InputState {
    pub fn clear(&mut self) {
        self.movement_vector = Vec2::ZERO;
        self.jump_pressed = false;
        self.attack_pressed = false;
        self.interact_pressed = false;
    }
}
