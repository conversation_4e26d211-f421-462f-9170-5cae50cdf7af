{"rustc": 15597765236515928571, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 10256229480693625789, "path": 13460280506177051175, "deps": [[555019317135488525, "regex_automata", false, 6370544720582857261], [2779309023524819297, "aho_corasick", false, 8080971413668539311], [9408802513701742484, "regex_syntax", false, 15608324139390986141], [15932120279885307830, "memchr", false, 1006342663428058179]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-06b64df7a9171f3d/dep-lib-regex", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}