{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"async-io\", \"multi-threaded\"]", "target": 17688444461467619473, "profile": 10256229480693625789, "path": 14291262406994415069, "deps": [[867502981669738401, "async_task", false, 16517979979977753503], [4221277904369061929, "async_executor", false, 2685547118824874884], [5302544599749092241, "async_channel", false, 8730797983012696193], [9570980159325712564, "futures_lite", false, 16446251869813219493], [12100481297174703255, "concurrent_queue", false, 12466861766262298885]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_tasks-d993462eace9353f/dep-lib-bevy_tasks", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}