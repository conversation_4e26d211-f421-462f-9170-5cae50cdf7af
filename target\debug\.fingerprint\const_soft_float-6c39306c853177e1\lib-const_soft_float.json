{"rustc": 15597765236515928571, "features": "[\"no_std\"]", "declared_features": "[\"const_mut_refs\", \"const_trait_impl\", \"no_std\"]", "target": 16731574752540088346, "profile": 10256229480693625789, "path": 17358467525997841789, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/const_soft_float-6c39306c853177e1/dep-lib-const_soft_float", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}