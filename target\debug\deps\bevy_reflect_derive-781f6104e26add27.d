/mnt/f/Epoch of Elria/target/debug/deps/libbevy_reflect_derive-781f6104e26add27.so: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/container_attributes.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/derive_data.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/enum_utility.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/field_attributes.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/from_reflect.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/enums.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/structs.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/tuple_structs.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/typed.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/values.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/reflect_value.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/registration.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/serialization.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/trait_reflection.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/type_path.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/type_uuid.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/utility.rs

/mnt/f/Epoch of Elria/target/debug/deps/bevy_reflect_derive-781f6104e26add27.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/container_attributes.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/derive_data.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/enum_utility.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/field_attributes.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/from_reflect.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/enums.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/structs.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/tuple_structs.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/typed.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/values.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/reflect_value.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/registration.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/serialization.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/trait_reflection.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/type_path.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/type_uuid.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/utility.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/container_attributes.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/derive_data.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/enum_utility.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/field_attributes.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/from_reflect.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/enums.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/structs.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/tuple_structs.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/typed.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/impls/values.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/reflect_value.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/registration.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/serialization.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/trait_reflection.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/type_path.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/type_uuid.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bevy_reflect_derive-0.12.1/src/utility.rs:
