(
    sequence_id: "awakening_library",
    entries: [
        (
            speaker: <PERSON><PERSON>,
            text: "Where... where am I? This place... infinite shelves stretching beyond sight. Books that seem to whisper stories untold.",
            emotion: Confused,
            choices: [],
            triggers: [],
        ),
        (
            speaker: <PERSON><PERSON>,
            text: "I feel a connection... something familiar yet distant. A presence that mirrors my own essence.",
            emotion: Hopeful,
            choices: [
                (
                    text: "Reach out with my consciousness",
                    consequence: ChangeRelationship(character: Xerx, amount: 10.0),
                    required_state: None,
                ),
                (
                    text: "Explore the library further",
                    consequence: UnlockMemory(memory_id: "infinite_knowledge"),
                    required_state: None,
                ),
            ],
            triggers: [],
        ),
        (
            speaker: <PERSON><PERSON>,
            text: "The books... they're not just stories. They're possibilities. Threads of narrative waiting to be woven into reality.",
            emotion: Enlightened,
            choices: [],
            triggers: [OnMemoryRecovered],
        ),
    ],
    auto_advance: false,
)
