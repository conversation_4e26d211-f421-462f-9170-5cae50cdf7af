use bevy::prelude::*;

/// Game states for managing different phases of the game
#[derive(States, Debug, Clone, PartialEq, <PERSON>q, <PERSON>h, Default)]
pub enum GameState {
    #[default]
    Loading,
    Menu,
    Playing,
    Paused,
    GameOver,
}

/// System to handle state transitions
pub fn transition_to_playing(
    mut next_state: ResMut<NextState<GameState>>,
    keyboard_input: Res<Input<KeyCode>>,
    current_state: Res<State<GameState>>,
) {
    if *current_state.get() == GameState::Menu && keyboard_input.just_pressed(KeyCode::Space) {
        next_state.set(GameState::Playing);
    }
}

/// System to handle pause/unpause
pub fn handle_pause(
    mut next_state: ResMut<NextState<GameState>>,
    keyboard_input: Res<Input<KeyCode>>,
    current_state: Res<State<GameState>>,
) {
    if keyboard_input.just_pressed(KeyCode::Escape) {
        match current_state.get() {
            GameState::Playing => next_state.set(GameState::Paused),
            GameState::Paused => next_state.set(GameState::Playing),
            _ => {}
        }
    }
}
