use bevy::prelude::*;
use crate::components::*;
use crate::resources::*;

/// Game states for managing different phases of the narrative
#[derive(States, Debug, Clone, PartialEq, Eq, Hash, Default)]
pub enum GameState {
    #[default]
    Loading,
    MainMenu,
    StoryIntro,
    InfiniteLibrary,    // <PERSON><PERSON>'s awakening
    SterileReality,     // Xerx's domain
    DreamRealm,         // Shared space
    Metaverse,          // Unwritten dreams
    NarrativeWeaving,   // Active story creation
    RealityStorm,       // Conflict with The One
    Paused,
    StoryComplete,
}

/// Reality layer states for managing different dimensional spaces
#[derive(States, Debug, Clone, PartialEq, Eq, Hash, Default)]
pub enum RealityState {
    #[default]
    Stable,
    Transitioning,
    Glitching,
    Collapsing,
    Reforming,
}

/// System to handle state transitions
pub fn transition_to_playing(
    mut next_state: ResMut<NextState<GameState>>,
    keyboard_input: Res<Input<KeyCode>>,
    current_state: Res<State<GameState>>,
) {
    if *current_state.get() == GameState::Menu && keyboard_input.just_pressed(KeyCode::Space) {
        next_state.set(GameState::Playing);
    }
}

/// System to handle pause/unpause
pub fn handle_pause(
    mut next_state: ResMut<NextState<GameState>>,
    keyboard_input: Res<Input<KeyCode>>,
    current_state: Res<State<GameState>>,
) {
    if keyboard_input.just_pressed(KeyCode::Escape) {
        match current_state.get() {
            GameState::Playing => next_state.set(GameState::Paused),
            GameState::Paused => next_state.set(GameState::Playing),
            _ => {}
        }
    }
}
