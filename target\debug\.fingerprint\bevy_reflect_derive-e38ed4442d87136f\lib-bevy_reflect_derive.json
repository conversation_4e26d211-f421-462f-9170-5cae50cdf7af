{"rustc": 15597765236515928571, "features": "[\"default\"]", "declared_features": "[\"default\", \"documentation\"]", "target": 18421881531529266918, "profile": 8285369720897779924, "path": 15280898567088457607, "deps": [[3060637413840920116, "proc_macro2", false, 4041766424894118187], [8319709847752024821, "uuid", false, 54057483266527176], [10640660562325816595, "syn", false, 8869189412150856287], [13800953573317674826, "bevy_macro_utils", false, 16887443781261048461], [17990358020177143287, "quote", false, 12542983872749336304]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_reflect_derive-e38ed4442d87136f/dep-lib-bevy_reflect_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}