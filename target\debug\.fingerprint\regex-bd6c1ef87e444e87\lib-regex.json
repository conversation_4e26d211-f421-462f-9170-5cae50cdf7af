{"rustc": 16591470773350601817, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 6942269584725465202, "path": 8951051242614557546, "deps": [[555019317135488525, "regex_automata", false, 9180952164731152881], [2779309023524819297, "aho_corasick", false, 2572134677197285068], [9408802513701742484, "regex_syntax", false, 11032971646845085396], [15932120279885307830, "memchr", false, 10140674662722423975]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-bd6c1ef87e444e87\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}