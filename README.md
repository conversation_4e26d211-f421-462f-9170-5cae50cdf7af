# Epoch of Elria

An optimized Rust game built with the Bevy engine, featuring high-performance architecture and modern game development practices.

## Features

- **High Performance**: Optimized build profiles and efficient ECS architecture
- **Modular Design**: Plugin-based architecture for easy extensibility
- **State Management**: Robust game state system
- **Asset Management**: Efficient asset loading and caching
- **Cross-Platform**: Supports Windows, macOS, and Linux

## Quick Start

### Prerequisites

1. Install Rust: https://rustup.rs/
2. Install Git: https://git-scm.com/

### Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd epoch-of-elria

# Build and run in development mode
cargo run

# Build optimized release version
cargo build --release
```

### Development

```bash
# Run with fast compilation (development)
cargo run --profile fast-dev

# Run tests
cargo test

# Run benchmarks
cargo bench

# Format code
cargo fmt

# Lint code
cargo clippy
```

## Architecture

### Core Components

- **ECS System**: Entity-Component-System architecture using Bevy
- **Plugin System**: Modular plugins for different game systems
- **State Management**: Game state transitions and management
- **Resource Management**: Efficient resource handling and caching

### Performance Optimizations

- **Build Profiles**: Multiple optimized build configurations
- **Memory Management**: Efficient memory usage patterns
- **Parallel Processing**: Multi-threaded system execution
- **Asset Streaming**: Optimized asset loading and caching

## Project Structure

```
src/
├── main.rs          # Application entry point
├── components.rs    # Game components
├── systems.rs       # Game systems
├── resources.rs     # Game resources
├── plugins.rs       # Plugin implementations
└── states.rs        # Game state management

assets/              # Game assets
├── sprites/         # Textures and sprites
├── audio/           # Sound effects and music
├── fonts/           # Font files
└── data/            # Configuration files

.cargo/              # Cargo configuration
└── config.toml      # Build optimizations
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under MIT OR Apache-2.0.
