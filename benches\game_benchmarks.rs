use criterion::{black_box, criterion_group, criterion_main, Criterion};
use bevy::prelude::*;

// Import your game modules
// use epoch_of_elria::*;

fn benchmark_entity_spawning(c: &mut Criterion) {
    c.bench_function("entity_spawning", |b| {
        b.iter(|| {
            let mut world = World::new();
            
            // Benchmark spawning 1000 entities
            for i in 0..1000 {
                world.spawn((
                    Transform::from_xyz(i as f32, 0.0, 0.0),
                    // Add other components as needed
                ));
            }
            
            black_box(world);
        });
    });
}

fn benchmark_system_execution(c: &mut Criterion) {
    c.bench_function("system_execution", |b| {
        let mut world = World::new();
        let mut schedule = Schedule::default();
        
        // Add systems to benchmark
        // schedule.add_systems(your_system);
        
        // Spawn test entities
        for i in 0..1000 {
            world.spawn(Transform::from_xyz(i as f32, 0.0, 0.0));
        }
        
        b.iter(|| {
            schedule.run(&mut world);
            black_box(&world);
        });
    });
}

fn benchmark_collision_detection(c: &mut Criterion) {
    c.bench_function("collision_detection", |b| {
        b.iter(|| {
            // Benchmark collision detection algorithms
            let entities = (0..100).map(|i| {
                (i as f32 * 10.0, i as f32 * 10.0, 32.0) // x, y, radius
            }).collect::<Vec<_>>();
            
            let mut collisions = 0;
            for i in 0..entities.len() {
                for j in i+1..entities.len() {
                    let (x1, y1, r1) = entities[i];
                    let (x2, y2, r2) = entities[j];
                    let distance = ((x2 - x1).powi(2) + (y2 - y1).powi(2)).sqrt();
                    if distance < r1 + r2 {
                        collisions += 1;
                    }
                }
            }
            
            black_box(collisions);
        });
    });
}

criterion_group!(
    benches,
    benchmark_entity_spawning,
    benchmark_system_execution,
    benchmark_collision_detection
);
criterion_main!(benches);
