{"rustc": 15597765236515928571, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"serde\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 10256229480693625789, "path": 15815845805274918467, "deps": [[966925859616469517, "ahash", false, 16281851950549932759], [9150530836556604396, "allocator_api2", false, 11527113942903132815], [9689903380558560274, "serde", false, 11908065706694279725]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-fcdd1f2e20c11fb4/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}