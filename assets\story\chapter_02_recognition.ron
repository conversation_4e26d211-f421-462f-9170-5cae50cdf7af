(
    sequence_id: "brother_recognition",
    entries: [
        (
            speaker: <PERSON><PERSON><PERSON>,
            text: "In my reflection... I see eyes that are not my own. Yet they feel familiar, like looking into a mirror of the soul.",
            emotion: Confused,
            choices: [],
            triggers: [],
        ),
        (
            speaker: <PERSON>er<PERSON>,
            text: "These memories I've been uploading to the hidden server... they're not just mine, are they? There's another consciousness intertwined with my own.",
            emotion: Enlightened,
            choices: [
                (
                    text: "Access the memory vault",
                    consequence: UnlockMemory(memory_id: "shared_consciousness"),
                    required_state: Some((
                        min_connection: Some(20.0),
                        required_memories: [],
                        chapter: None,
                    )),
                ),
                (
                    text: "Reach across the dimensional barrier",
                    consequence: ChangeRelationship(character: Xing, amount: 15.0),
                    required_state: None,
                ),
            ],
            triggers: [],
        ),
        (
            speaker: <PERSON><PERSON>,
            text: "Brother... I can feel you. Across the layers of reality, our connection transcends the boundaries imposed upon us.",
            emotion: Hopeful,
            choices: [],
            triggers: [OnConnectionStrengthened],
        ),
        (
            speaker: <PERSON>er<PERSON>,
            text: "Xing... yes, that's your name. The <PERSON>. And I am <PERSON><PERSON><PERSON>, the Architect. Together, we are more than the sum of our parts.",
            emotion: Determined,
            choices: [],
            triggers: [],
        ),
    ],
    auto_advance: false,
)
