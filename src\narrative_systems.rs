use bevy::prelude::*;
use crate::components::*;
use crate::resources::*;
use crate::states::*;

/// System for managing narrative threads and story connections
pub fn narrative_weaving_system(
    mut commands: Commands,
    mut weaver_query: Query<(&mut WeavingAbility, &Transform, &EmotionalState), With<Character>>,
    thread_query: Query<&NarrativeThread>,
    mut narrative_state: ResMut<NarrativeState>,
    time: Res<Time>,
) {
    for (mut weaving, transform, emotional_state) in weaver_query.iter_mut() {
        // Weaving power influenced by emotional state
        let weaving_modifier = (emotional_state.hope + emotional_state.determination) / 100.0;
        weaving.reality_influence = weaving.pattern_complexity * weaving_modifier;
        
        // Create new narrative threads based on proximity and emotional resonance
        if emotional_state.connection > 70.0 {
            let new_thread = NarrativeThread {
                thread_id: narrative_state.next_thread_id(),
                strength: emotional_state.connection,
                connected_entities: Vec::new(),
                story_weight: weaving.reality_influence,
            };
            
            commands.spawn(new_thread);
        }
    }
}

/// System for managing reality layer transitions
pub fn reality_transition_system(
    mut query: Query<(&mut RealityLayer, &Character, &EmotionalState)>,
    mut transition_events: EventWriter<RealityTransitionEvent>,
    keyboard_input: Res<Input<KeyCode>>,
) {
    for (mut reality_layer, character, emotional_state) in query.iter_mut() {
        let can_transition = match character.character_type {
            CharacterType::Xing => emotional_state.dream_affinity > 80.0,
            CharacterType::Xerx => emotional_state.determination > 70.0,
            CharacterType::Heart => true, // Heart can move freely
            CharacterType::TheOne => emotional_state.fear < 30.0, // The One moves when confident
        };
        
        if can_transition && keyboard_input.just_pressed(KeyCode::Tab) {
            let new_layer = match *reality_layer {
                RealityLayer::InfiniteLibrary => RealityLayer::DreamRealm,
                RealityLayer::SterileReality => RealityLayer::DreamRealm,
                RealityLayer::DreamRealm => RealityLayer::Metaverse,
                RealityLayer::Metaverse => RealityLayer::InfiniteLibrary,
                RealityLayer::TheVoid => RealityLayer::Metaverse,
            };
            
            *reality_layer = new_layer;
            transition_events.send(RealityTransitionEvent {
                character: character.character_type,
                from: *reality_layer,
                to: new_layer,
            });
        }
    }
}

/// System for managing memory fragments and their effects
pub fn memory_system(
    mut memory_query: Query<&mut MemoryFragment>,
    character_query: Query<(&Character, &EmotionalState)>,
    mut memory_events: EventWriter<MemoryEvent>,
    time: Res<Time>,
) {
    for mut memory in memory_query.iter_mut() {
        // Memory clarity degrades over time unless actively maintained
        if !memory.is_suppressed {
            memory.clarity -= time.delta_seconds() * 0.1;
            memory.clarity = memory.clarity.max(0.0);
        }
        
        // Check for memory recovery events
        for (character, emotional_state) in character_query.iter() {
            if character.character_type == memory.origin_character {
                if emotional_state.connection > 80.0 && memory.is_suppressed {
                    memory.is_suppressed = false;
                    memory.clarity += 20.0;
                    memory_events.send(MemoryEvent::Recovered {
                        character: character.character_type,
                        memory_content: memory.content.clone(),
                    });
                }
            }
        }
    }
}

/// System for managing glitch effects and reality distortions
pub fn glitch_system(
    mut commands: Commands,
    mut glitch_query: Query<(Entity, &mut GlitchEffect, &mut Transform)>,
    the_one_query: Query<&Transform, (With<Character>, Without<GlitchEffect>)>,
    time: Res<Time>,
) {
    for (entity, mut glitch, mut transform) in glitch_query.iter_mut() {
        glitch.duration.tick(time.delta());
        
        // Apply glitch visual effects
        match glitch.glitch_type {
            GlitchType::RealityFlicker => {
                let flicker = (time.elapsed_seconds() * glitch.frequency).sin() * glitch.intensity;
                transform.scale = Vec3::splat(1.0 + flicker * 0.1);
            }
            GlitchType::IdentityBlur => {
                let blur = (time.elapsed_seconds() * glitch.frequency * 2.0).cos() * glitch.intensity;
                transform.rotation = Quat::from_rotation_z(blur * 0.05);
            }
            GlitchType::MemoryCorruption => {
                // Memory corruption affects position stability
                let corruption = (time.elapsed_seconds() * glitch.frequency * 3.0).sin() * glitch.intensity;
                transform.translation.x += corruption * 2.0;
            }
            GlitchType::NarrativeBreak => {
                // Narrative breaks cause random displacement
                if glitch.duration.percent() > 0.5 {
                    transform.translation += Vec3::new(
                        (rand::random::<f32>() - 0.5) * glitch.intensity,
                        (rand::random::<f32>() - 0.5) * glitch.intensity,
                        0.0,
                    );
                }
            }
        }
        
        // Remove expired glitches
        if glitch.duration.finished() {
            commands.entity(entity).remove::<GlitchEffect>();
        }
    }
}

/// System for managing The One's influence and order imposition
pub fn the_one_influence_system(
    mut commands: Commands,
    the_one_query: Query<(&Transform, &Character), With<Character>>,
    mut other_characters: Query<(&mut EmotionalState, &Transform, &Character), Without<Character>>,
    mut narrative_state: ResMut<NarrativeState>,
) {
    for (the_one_transform, the_one_character) in the_one_query.iter() {
        if the_one_character.character_type != CharacterType::TheOne {
            continue;
        }
        
        // The One's influence spreads in a radius, imposing order
        for (mut emotional_state, transform, character) in other_characters.iter_mut() {
            let distance = the_one_transform.translation.distance(transform.translation);
            let influence_radius = 200.0;
            
            if distance < influence_radius {
                let influence_strength = (influence_radius - distance) / influence_radius;
                
                // The One suppresses hope and connection, increases fear
                emotional_state.hope -= influence_strength * 10.0;
                emotional_state.connection -= influence_strength * 15.0;
                emotional_state.fear += influence_strength * 20.0;
                
                // Clamp values
                emotional_state.hope = emotional_state.hope.max(0.0);
                emotional_state.connection = emotional_state.connection.max(0.0);
                emotional_state.fear = emotional_state.fear.min(100.0);
                
                // Create glitch effects under strong influence
                if influence_strength > 0.7 {
                    commands.spawn(GlitchEffect {
                        intensity: influence_strength,
                        frequency: 5.0,
                        glitch_type: GlitchType::MemoryCorruption,
                        duration: Timer::from_seconds(2.0, TimerMode::Once),
                    });
                }
            }
        }
        
        // The One reduces overall narrative freedom
        narrative_state.freedom_level -= 0.1;
        narrative_state.freedom_level = narrative_state.freedom_level.max(0.0);
    }
}

/// Events for narrative system communication
#[derive(Event)]
pub enum RealityTransitionEvent {
    Transition {
        character: CharacterType,
        from: RealityLayer,
        to: RealityLayer,
    },
}

#[derive(Event)]
pub enum MemoryEvent {
    Recovered {
        character: CharacterType,
        memory_content: String,
    },
    Suppressed {
        character: CharacterType,
        memory_content: String,
    },
}

#[derive(Event)]
pub struct NarrativeWeavingEvent {
    pub weaver: CharacterType,
    pub thread_strength: f32,
    pub affected_entities: Vec<Entity>,
}
