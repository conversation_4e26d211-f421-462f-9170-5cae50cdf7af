{"rustc": 15597765236515928571, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 8285369720897779924, "path": 1392751382480501338, "deps": [[1988483478007900009, "unicode_ident", false, 3040696267119389465], [3060637413840920116, "proc_macro2", false, 4041766424894118187], [17990358020177143287, "quote", false, 12542983872749336304]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-cec415a192775bac/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}