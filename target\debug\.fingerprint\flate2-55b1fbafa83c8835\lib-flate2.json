{"rustc": 15597765236515928571, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 10256229480693625789, "path": 15651550432702689603, "deps": [[5466618496199522463, "crc32fast", false, 67292085516880370], [7636735136738807108, "miniz_oxide", false, 16830025946353384680]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-55b1fbafa83c8835/dep-lib-flate2", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}