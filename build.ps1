# Build script for The Dream Weaver's Heart - Epoch of Elria
# Optimized Rust game build automation

Write-Host "🎮 Building The Dream Weaver's Heart - Epoch of Elria" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Check if Rust is installed
try {
    $rustVersion = cargo --version
    Write-Host "✅ Rust found: $rustVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Rust not found. Installing Rust..." -ForegroundColor Red
    
    # Download and install Rust
    Write-Host "📥 Downloading Rust installer..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "rustup-init.exe"
    
    Write-Host "🔧 Installing Rust (this may take a few minutes)..." -ForegroundColor Yellow
    .\rustup-init.exe -y --default-toolchain stable
    
    # Add Rust to PATH for current session
    $env:PATH += ";$env:USERPROFILE\.cargo\bin"
    
    # Clean up installer
    Remove-Item "rustup-init.exe" -ErrorAction SilentlyContinue
    
    Write-Host "✅ Rust installation complete!" -ForegroundColor Green
}

# Install additional components
Write-Host "🔧 Installing additional Rust components..." -ForegroundColor Yellow
rustup component add clippy rustfmt

# Build the game
Write-Host "🏗️  Building the game..." -ForegroundColor Yellow

# Development build (faster compilation)
Write-Host "Building development version..." -ForegroundColor Cyan
cargo build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Development build successful!" -ForegroundColor Green
    
    # Ask if user wants to build release version
    $buildRelease = Read-Host "Build optimized release version? (y/N)"
    if ($buildRelease -eq "y" -or $buildRelease -eq "Y") {
        Write-Host "🚀 Building optimized release version..." -ForegroundColor Yellow
        cargo build --release
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Release build successful!" -ForegroundColor Green
        } else {
            Write-Host "❌ Release build failed!" -ForegroundColor Red
        }
    }
    
    # Ask if user wants to run the game
    $runGame = Read-Host "Run the game now? (Y/n)"
    if ($runGame -ne "n" -and $runGame -ne "N") {
        Write-Host "🎮 Starting The Dream Weaver's Heart..." -ForegroundColor Cyan
        cargo run
    }
    
} else {
    Write-Host "❌ Build failed! Check the error messages above." -ForegroundColor Red
    Write-Host "💡 Common solutions:" -ForegroundColor Yellow
    Write-Host "   - Make sure all dependencies are available" -ForegroundColor White
    Write-Host "   - Check that you have the latest Rust version" -ForegroundColor White
    Write-Host "   - Try: cargo clean && cargo build" -ForegroundColor White
}

Write-Host "`n🎭 The Dream Weaver's Heart - Ready to explore the narrative!" -ForegroundColor Magenta
Write-Host "=================================================" -ForegroundColor Cyan
