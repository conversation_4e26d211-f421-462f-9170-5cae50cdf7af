{"rustc": 15597765236515928571, "features": "[\"default\", \"regex-syntax\", \"std\"]", "declared_features": "[\"default\", \"fst\", \"regex-syntax\", \"std\", \"transducer\"]", "target": 189779444668410301, "profile": 10256229480693625789, "path": 1597865956014254447, "deps": [[7982432068776955834, "regex_syntax", false, 1726671626089401561]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-automata-644787133e9c4e14/dep-lib-regex_automata", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}