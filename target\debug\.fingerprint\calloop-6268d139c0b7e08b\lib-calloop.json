{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"executor\", \"futures-io\", \"futures-util\"]", "target": 13000572321397389619, "profile": 10256229480693625789, "path": 18253920428098471060, "deps": [[3812455420980172077, "nix", false, 16356402620759702667], [5986029879202738730, "log", false, 9222922025880726257], [6997951260445117383, "slotmap", false, 11821519526717451353], [8008191657135824715, "thiserror", false, 1414080933147118704], [10435729446543529114, "bitflags", false, 11912335317805936274], [14451951854123638585, "vec_map", false, 6839705248536098457]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/calloop-6268d139c0b7e08b/dep-lib-calloop", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}