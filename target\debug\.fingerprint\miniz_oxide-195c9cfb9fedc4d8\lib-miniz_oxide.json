{"rustc": 15597765236515928571, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 1086714531291423172, "path": 13326185721199898267, "deps": [[4018467389006652250, "simd_adler32", false, 17342108889690210837], [7911289239703230891, "adler2", false, 16929567249444194179]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-195c9cfb9fedc4d8/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}