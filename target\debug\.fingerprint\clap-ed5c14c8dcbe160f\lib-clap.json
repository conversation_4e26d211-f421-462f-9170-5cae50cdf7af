{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 4249017621561997261, "path": 15102792655553339975, "deps": [[14814905555676593471, "clap_builder", false, 16308986930576332820]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-ed5c14c8dcbe160f\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}