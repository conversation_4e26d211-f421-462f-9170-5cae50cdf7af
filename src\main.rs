use bevy::prelude::*;

mod components;
mod systems;
mod resources;
mod plugins;
mod states;

use components::*;
use systems::*;
use resources::*;
use plugins::*;
use states::*;

fn main() {
    App::new()
        // Core Bevy plugins with optimized settings
        .add_plugins(DefaultPlugins.set(WindowPlugin {
            primary_window: Some(Window {
                title: "Epoch of Elria".into(),
                resolution: (1280.0, 720.0).into(),
                present_mode: bevy::window::PresentMode::AutoVsync,
                ..default()
            }),
            ..default()
        }))
        
        // Game state management
        .add_state::<GameState>()
        
        // Custom game plugins
        .add_plugins(GamePlugin)
        .add_plugins(PlayerPlugin)
        .add_plugins(WorldPlugin)
        .add_plugins(UIPlugin)
        
        // Resources
        .insert_resource(GameSettings::default())
        .insert_resource(GameStats::default())
        
        // Startup systems
        .add_systems(Startup, (
            setup_camera,
            setup_world,
            load_assets,
        ))
        
        // Update systems organized by state
        .add_systems(Update, (
            handle_input,
            update_player,
            update_world,
            update_ui,
        ).run_if(in_state(GameState::Playing)))
        
        .add_systems(Update, (
            menu_system,
        ).run_if(in_state(GameState::Menu)))
        
        // Performance monitoring (debug only)
        #[cfg(debug_assertions)]
        .add_plugins(bevy::diagnostic::FrameTimeDiagnosticsPlugin::default())
        #[cfg(debug_assertions)]
        .add_plugins(bevy::diagnostic::EntityCountDiagnosticsPlugin::default())
        
        .run();
}

// Camera setup with optimal settings
fn setup_camera(mut commands: Commands) {
    commands.spawn(Camera2dBundle::default());
}

// World initialization
fn setup_world(mut commands: Commands) {
    info!("Setting up game world...");
    // World setup will be implemented in world plugin
}

// Asset loading system
fn load_assets(mut commands: Commands, asset_server: Res<AssetServer>) {
    info!("Loading game assets...");
    // Asset loading will be implemented in asset plugin
}
