{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9241925498456048256, "build_script_build", false, 9266819437335549564]], "local": [{"RerunIfChanged": {"output": "debug/build/blake3-a3f368e65ba5e136/output", "paths": ["c/blake3_sse41_x86-64_windows_gnu.S", "c/blake3_avx512_x86-64_windows_msvc.asm", "c/blake3_dispatch.c", "c/blake3_avx512_x86-64_windows_gnu.S", "c/blake3_sse2_x86-64_windows_gnu.S", "c/test.py", "c/blake3_tbb.cpp", "c/blake3_sse2.c", "c/blake3_avx512_x86-64_unix.S", "c/blake3_avx2.c", "c/example_tbb.c", "c/blake3_sse41_x86-64_unix.S", "c/blake3.c", "c/CMakePresets.json", "c/blake3_sse2_x86-64_windows_msvc.asm", "c/cmake", "c/libblake3.pc.in", "c/main.c", "c/blake3_sse41_x86-64_windows_msvc.asm", "c/blake3_sse2_x86-64_unix.S", "c/Makefile.testing", "c/blake3_sse41.c", "c/blake3-config.cmake.in", "c/blake3_impl.h", "c/blake3_avx2_x86-64_windows_gnu.S", "c/example.c", "c/dependencies", "c/blake3.h", "c/.giti<PERSON>re", "c/blake3_portable.c", "c/blake3_neon.c", "c/CMakeLists.txt", "c/blake3_avx512.c", "c/blake3_avx2_x86-64_windows_msvc.asm", "c/blake3_avx2_x86-64_unix.S", "c/README.md"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}