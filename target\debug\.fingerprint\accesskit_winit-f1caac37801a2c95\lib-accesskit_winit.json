{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"accesskit_unix\", \"async-io\", \"default\", \"tokio\"]", "target": 9844795606708974272, "profile": 10256229480693625789, "path": 1483719934638733324, "deps": [[2111037739697570178, "accesskit", false, 13737960786648319506], [10036841630170532596, "winit", false, 7162149633860407712]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/accesskit_winit-f1caac37801a2c95/dep-lib-accesskit_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}