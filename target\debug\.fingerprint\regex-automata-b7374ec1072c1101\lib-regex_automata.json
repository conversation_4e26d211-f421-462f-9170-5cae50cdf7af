{"rustc": 16591470773350601817, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 6942269584725465202, "path": 9200316565361829926, "deps": [[2779309023524819297, "aho_corasick", false, 2572134677197285068], [9408802513701742484, "regex_syntax", false, 11032971646845085396], [15932120279885307830, "memchr", false, 10140674662722423975]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-automata-b7374ec1072c1101\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}