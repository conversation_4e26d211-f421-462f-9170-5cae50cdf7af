{"rustc": 15597765236515928571, "features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "declared_features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "target": 6885752962907571681, "profile": 10256229480693625789, "path": 13059697775384988561, "deps": [[5330658427305787935, "libc", false, 7777317402054859359], [9011620124493104570, "build_script_build", false, 147331271688837187], [9223765023884963089, "nix", false, 17307037218135996831], [10435729446543529114, "bitflags", false, 11912335317805936274], [11434239582363224126, "downcast_rs", false, 10789207993170213021], [12570133544693084162, "wayland_sys", false, 14391034201947996325], [13370890382188185363, "scoped_tls", false, 186318697411955629], [14682610449618970944, "wayland_commons", false, 15004023166417683261]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-client-864b4a1a148adeef/dep-lib-wayland_client", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}