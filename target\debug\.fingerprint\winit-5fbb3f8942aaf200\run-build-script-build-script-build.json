{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10036841630170532596, "build_script_build", false, 2775094430356604080]], "local": [{"RerunIfChanged": {"output": "debug/build/winit-5fbb3f8942aaf200/output", "paths": ["build.rs", "wayland_protocols"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 0, "compile_kind": 0}