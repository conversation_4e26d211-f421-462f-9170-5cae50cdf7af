{"rustc": 15597765236515928571, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 6661914849467474781, "path": 10707664996105353335, "deps": [[189982446159473706, "parking", false, 11135986544737782046], [1906322745568073236, "pin_project_lite", false, 4512339335007465936], [12100481297174703255, "concurrent_queue", false, 12466861766262298885]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/event-listener-5f5f7cd12fadcd1f/dep-lib-event_listener", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}