use bevy::prelude::*;

/// Player component - marks entities as player-controlled
#[derive(Component)]
pub struct Player {
    pub speed: f32,
    pub health: f32,
    pub max_health: f32,
}

impl Default for Player {
    fn default() -> Self {
        Self {
            speed: 200.0,
            health: 100.0,
            max_health: 100.0,
        }
    }
}

/// Velocity component for physics-like movement
#[derive(Component, Default)]
pub struct Velocity {
    pub x: f32,
    pub y: f32,
}

impl Velocity {
    pub fn new(x: f32, y: f32) -> Self {
        Self { x, y }
    }
    
    pub fn zero() -> Self {
        Self { x: 0.0, y: 0.0 }
    }
}

/// Health component for entities that can take damage
#[derive(Component)]
pub struct Health {
    pub current: f32,
    pub max: f32,
}

impl Health {
    pub fn new(max: f32) -> Self {
        Self {
            current: max,
            max,
        }
    }
    
    pub fn is_alive(&self) -> bool {
        self.current > 0.0
    }
    
    pub fn take_damage(&mut self, damage: f32) {
        self.current = (self.current - damage).max(0.0);
    }
    
    pub fn heal(&mut self, amount: f32) {
        self.current = (self.current + amount).min(self.max);
    }
}

/// Animation component for sprite animations
#[derive(Component)]
pub struct AnimationTimer {
    pub timer: Timer,
    pub frame_count: usize,
    pub current_frame: usize,
}

impl AnimationTimer {
    pub fn new(duration_per_frame: f32, frame_count: usize) -> Self {
        Self {
            timer: Timer::from_seconds(duration_per_frame, TimerMode::Repeating),
            frame_count,
            current_frame: 0,
        }
    }
}

/// Enemy component
#[derive(Component)]
pub struct Enemy {
    pub damage: f32,
    pub attack_range: f32,
}

/// Collectible item component
#[derive(Component)]
pub struct Collectible {
    pub value: i32,
    pub item_type: CollectibleType,
}

#[derive(Clone, Copy)]
pub enum CollectibleType {
    Coin,
    HealthPotion,
    PowerUp,
}

/// UI component markers
#[derive(Component)]
pub struct HealthBar;

#[derive(Component)]
pub struct ScoreText;

#[derive(Component)]
pub struct MenuButton;
