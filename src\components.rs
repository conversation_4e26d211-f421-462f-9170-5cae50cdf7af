use bevy::prelude::*;
use serde::{Deserialize, Serialize};

/// Character archetypes for the story
#[derive(<PERSON>mpo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum CharacterType {
    Xing,  // The Weaver
    Xerx,  // The Architect
    TheOne, // The antagonist entity
    Heart,  // The catalyst entity
}

/// Core character component with story-specific attributes
#[derive(Component)]
pub struct Character {
    pub character_type: CharacterType,
    pub narrative_power: f32,    // Ability to influence reality
    pub connection_strength: f32, // Connection to other characters
    pub memory_integrity: f32,   // Resistance to reality manipulation
    pub dream_affinity: f32,     // Ability to navigate dream states
}

impl Character {
    pub fn xing() -> Self {
        Self {
            character_type: CharacterType::Xing,
            narrative_power: 100.0,
            connection_strength: 90.0,
            memory_integrity: 85.0,
            dream_affinity: 95.0,
        }
    }

    pub fn xerx() -> Self {
        Self {
            character_type: CharacterType::Xerx,
            narrative_power: 80.0,
            connection_strength: 90.0,
            memory_integrity: 70.0,
            dream_affinity: 60.0,
        }
    }

    pub fn the_heart() -> Self {
        Self {
            character_type: CharacterType::Heart,
            narrative_power: 150.0,
            connection_strength: 100.0,
            memory_integrity: 100.0,
            dream_affinity: 100.0,
        }
    }
}

/// Reality state component - tracks which layer of reality an entity exists in
#[derive(Component, Clone, Copy, PartialEq, Eq)]
pub enum RealityLayer {
    InfiniteLibrary,  // Xing's domain
    SterileReality,   // Xerx's domain
    DreamRealm,       // Shared dream space
    Metaverse,        // The unwritten dreams space
    TheVoid,          // The One's domain
}

/// Narrative thread component - represents story connections
#[derive(Component)]
pub struct NarrativeThread {
    pub thread_id: u32,
    pub strength: f32,
    pub connected_entities: Vec<Entity>,
    pub story_weight: f32,
}

/// Memory fragment component - represents stored experiences
#[derive(Component, Serialize, Deserialize)]
pub struct MemoryFragment {
    pub content: String,
    pub emotional_weight: f32,
    pub clarity: f32,
    pub is_suppressed: bool,
    pub origin_character: CharacterType,
}

/// Weaving ability component - Xing's reality manipulation
#[derive(Component)]
pub struct WeavingAbility {
    pub pattern_complexity: f32,
    pub reality_influence: f32,
    pub active_threads: Vec<u32>,
}

/// Architectural power component - Xerx's structural abilities
#[derive(Component)]
pub struct ArchitecturalPower {
    pub structural_integrity: f32,
    pub design_precision: f32,
    pub memory_vault_access: bool,
}

/// Emotional state component - tracks character emotions
#[derive(Component)]
pub struct EmotionalState {
    pub hope: f32,
    pub frustration: f32,
    pub fear: f32,
    pub determination: f32,
    pub connection: f32,
}

impl EmotionalState {
    pub fn balanced() -> Self {
        Self {
            hope: 50.0,
            frustration: 20.0,
            fear: 30.0,
            determination: 70.0,
            connection: 60.0,
        }
    }

    pub fn heart_state() -> Self {
        Self {
            hope: 100.0,
            frustration: 80.0,  // Impatience
            fear: 60.0,
            determination: 90.0,
            connection: 100.0,
        }
    }
}

/// Glitch effect component - represents reality distortions
#[derive(Component)]
pub struct GlitchEffect {
    pub intensity: f32,
    pub frequency: f32,
    pub glitch_type: GlitchType,
    pub duration: Timer,
}

#[derive(Clone, Copy)]
pub enum GlitchType {
    MemoryCorruption,
    RealityFlicker,
    IdentityBlur,
    NarrativeBreak,
}

/// Animation component for sprite animations
#[derive(Component)]
pub struct AnimationTimer {
    pub timer: Timer,
    pub frame_count: usize,
    pub current_frame: usize,
}

impl AnimationTimer {
    pub fn new(duration_per_frame: f32, frame_count: usize) -> Self {
        Self {
            timer: Timer::from_seconds(duration_per_frame, TimerMode::Repeating),
            frame_count,
            current_frame: 0,
        }
    }
}

/// Enemy component
#[derive(Component)]
pub struct Enemy {
    pub damage: f32,
    pub attack_range: f32,
}

/// Collectible item component
#[derive(Component)]
pub struct Collectible {
    pub value: i32,
    pub item_type: CollectibleType,
}

#[derive(Clone, Copy)]
pub enum CollectibleType {
    Coin,
    HealthPotion,
    PowerUp,
}

/// UI component markers
#[derive(Component)]
pub struct HealthBar;

#[derive(Component)]
pub struct ScoreText;

#[derive(Component)]
pub struct MenuButton;
