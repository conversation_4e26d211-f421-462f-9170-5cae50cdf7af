{"rustc": 15597765236515928571, "features": "[\"client\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\", \"wayland-server\"]", "target": 17851254929718682527, "profile": 10256229480693625789, "path": 15641918551923447096, "deps": [[2838496963530108853, "build_script_build", false, 4945277765631813969], [9011620124493104570, "wayland_client", false, 8124995950088047876], [10435729446543529114, "bitflags", false, 11912335317805936274], [14682610449618970944, "wayland_commons", false, 15004023166417683261]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-abf80c2149a70498/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}