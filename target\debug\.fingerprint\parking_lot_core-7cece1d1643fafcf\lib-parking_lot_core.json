{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 10256229480693625789, "path": 11546138597823357121, "deps": [[2828590642173593838, "cfg_if", false, 9111842139069350921], [3666196340704888985, "smallvec", false, 11275249618712392638], [4269498962362888130, "build_script_build", false, 10171635552630766952], [5330658427305787935, "libc", false, 7777317402054859359]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot_core-7cece1d1643fafcf/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}