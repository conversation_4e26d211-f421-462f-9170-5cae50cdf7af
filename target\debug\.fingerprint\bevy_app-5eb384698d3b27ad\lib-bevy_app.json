{"rustc": 15597765236515928571, "features": "[\"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_ci_testing\", \"bevy_reflect\", \"default\", \"ron\", \"serde\", \"trace\"]", "target": 15123732072264781070, "profile": 10256229480693625789, "path": 6661009256575287202, "deps": [[597746441010298004, "bevy_derive", false, 14738724730754875882], [923331430220980291, "bevy_tasks", false, 15773927132577434283], [1067171486353553040, "bevy_ecs", false, 4483169035599705488], [1720748814177985408, "bevy_reflect", false, 4562617993401875491], [8874671706155897836, "bevy_utils", false, 14158135305627284877], [11434239582363224126, "downcast_rs", false, 10789207993170213021]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_app-5eb384698d3b27ad/dep-lib-bevy_app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}