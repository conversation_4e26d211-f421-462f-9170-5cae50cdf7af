{"rustc": 15597765236515928571, "features": "[\"client\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging_protocols\", \"unstable_protocols\", \"wayland-client\", \"wayland-server\"]", "target": 17883862002600103897, "profile": 8285369720897779924, "path": 11748893103044632325, "deps": [[13211618713646843096, "wayland_scanner", false, 10295145333999681998]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-ec8ce41d4d9a1d79/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}