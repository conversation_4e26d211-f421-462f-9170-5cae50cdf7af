use bevy::prelude::*;
use crate::components::*;
use crate::resources::*;

/// Handle player input with optimized input processing
pub fn handle_input(
    keyboard_input: Res<Input<KeyCode>>,
    mut player_query: Query<&mut Velocity, With<Player>>,
    game_settings: Res<GameSettings>,
) {
    for mut velocity in player_query.iter_mut() {
        velocity.x = 0.0;
        velocity.y = 0.0;
        
        if keyboard_input.pressed(KeyCode::A) || keyboard_input.pressed(KeyCode::Left) {
            velocity.x -= game_settings.player_speed;
        }
        if keyboard_input.pressed(KeyCode::D) || keyboard_input.pressed(KeyCode::Right) {
            velocity.x += game_settings.player_speed;
        }
        if keyboard_input.pressed(KeyCode::W) || keyboard_input.pressed(KeyCode::Up) {
            velocity.y += game_settings.player_speed;
        }
        if keyboard_input.pressed(KeyCode::S) || keyboard_input.pressed(KeyCode::Down) {
            velocity.y -= game_settings.player_speed;
        }
    }
}

/// Update player position and state
pub fn update_player(
    time: Res<Time>,
    mut player_query: Query<(&mut Transform, &Velocity), With<Player>>,
) {
    for (mut transform, velocity) in player_query.iter_mut() {
        transform.translation.x += velocity.x * time.delta_seconds();
        transform.translation.y += velocity.y * time.delta_seconds();
        
        // Keep player within screen bounds (basic implementation)
        transform.translation.x = transform.translation.x.clamp(-640.0, 640.0);
        transform.translation.y = transform.translation.y.clamp(-360.0, 360.0);
    }
}

/// Update world entities and physics
pub fn update_world(
    time: Res<Time>,
    mut query: Query<(&mut Transform, &Velocity), Without<Player>>,
) {
    for (mut transform, velocity) in query.iter_mut() {
        transform.translation.x += velocity.x * time.delta_seconds();
        transform.translation.y += velocity.y * time.delta_seconds();
    }
}

/// Update UI elements
pub fn update_ui(
    mut health_bar_query: Query<&mut Style, With<HealthBar>>,
    player_query: Query<&Player>,
    mut score_query: Query<&mut Text, With<ScoreText>>,
    game_stats: Res<GameStats>,
) {
    // Update health bar
    if let Ok(player) = player_query.get_single() {
        for mut style in health_bar_query.iter_mut() {
            let health_percentage = player.health / player.max_health;
            style.width = Val::Percent(health_percentage * 100.0);
        }
    }
    
    // Update score
    for mut text in score_query.iter_mut() {
        text.sections[0].value = format!("Score: {}", game_stats.score);
    }
}

/// Simple menu system
pub fn menu_system(
    mut interaction_query: Query<
        (&Interaction, &mut BackgroundColor),
        (Changed<Interaction>, With<MenuButton>),
    >,
) {
    for (interaction, mut color) in interaction_query.iter_mut() {
        match *interaction {
            Interaction::Pressed => {
                *color = Color::rgb(0.35, 0.75, 0.35).into();
            }
            Interaction::Hovered => {
                *color = Color::rgb(0.25, 0.25, 0.25).into();
            }
            Interaction::None => {
                *color = Color::rgb(0.15, 0.15, 0.15).into();
            }
        }
    }
}

/// Animation system for sprite animations
pub fn animate_sprites(
    time: Res<Time>,
    mut query: Query<(&mut AnimationTimer, &mut TextureAtlasSprite)>,
) {
    for (mut timer, mut sprite) in query.iter_mut() {
        timer.timer.tick(time.delta());
        if timer.timer.just_finished() {
            timer.current_frame = (timer.current_frame + 1) % timer.frame_count;
            sprite.index = timer.current_frame;
        }
    }
}

/// Collision detection system (basic AABB)
pub fn collision_system(
    mut player_query: Query<(&Transform, &mut Health), With<Player>>,
    enemy_query: Query<&Transform, (With<Enemy>, Without<Player>)>,
) {
    for (player_transform, mut player_health) in player_query.iter_mut() {
        for enemy_transform in enemy_query.iter() {
            let distance = player_transform.translation.distance(enemy_transform.translation);
            if distance < 32.0 { // Basic collision threshold
                player_health.take_damage(10.0);
            }
        }
    }
}
