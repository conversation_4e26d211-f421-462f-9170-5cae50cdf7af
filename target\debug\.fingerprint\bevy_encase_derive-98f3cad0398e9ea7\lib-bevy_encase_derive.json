{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[]", "target": 14161042395232763202, "profile": 8285369720897779924, "path": 7626919419969566574, "deps": [[8081174564369101661, "encase_derive_impl", false, 9166350532408876986], [13800953573317674826, "bevy_macro_utils", false, 16887443781261048461]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_encase_derive-98f3cad0398e9ea7/dep-lib-bevy_encase_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}