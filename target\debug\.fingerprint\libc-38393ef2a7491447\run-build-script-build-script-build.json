{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5330658427305787935, "build_script_build", false, 3197978213159176611]], "local": [{"RerunIfChanged": {"output": "debug/build/libc-38393ef2a7491447/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_FREEBSD_VERSION", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_MUSL_V1_2_3", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_LINUX_TIME_BITS64", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_FILE_OFFSET_BITS", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_TIME_BITS", "val": null}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 0, "compile_kind": 0}