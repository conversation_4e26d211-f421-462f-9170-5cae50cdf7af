use bevy::prelude::*;
use serde::{Deserialize, Serialize};
use crate::components::*;
use crate::resources::*;

/// Dialogue system for character interactions and narrative delivery
#[derive(Resource)]
pub struct DialogueSystem {
    pub current_dialogue: Option<DialogueSequence>,
    pub dialogue_history: Vec<DialogueEntry>,
    pub character_voices: std::collections::HashMap<CharacterType, VoiceProfile>,
}

impl Default for DialogueSystem {
    fn default() -> Self {
        let mut character_voices = std::collections::HashMap::new();
        
        character_voices.insert(CharacterType::Xing, VoiceProfile {
            color: Color::rgb(0.7, 0.9, 1.0),  // Soft blue
            font_size: 18.0,
            speaking_style: SpeakingStyle::Contemplative,
        });
        
        character_voices.insert(CharacterType::Xerx, VoiceProfile {
            color: Color::rgb(1.0, 0.8, 0.6),  // Warm orange
            font_size: 18.0,
            speaking_style: SpeakingStyle::Analytical,
        });
        
        character_voices.insert(CharacterType::Heart, VoiceProfile {
            color: Color::rgb(1.0, 0.6, 0.8),  // Radiant pink
            font_size: 20.0,
            speaking_style: SpeakingStyle::Emotional,
        });
        
        character_voices.insert(CharacterType::TheOne, VoiceProfile {
            color: Color::rgb(0.3, 0.3, 0.3),  // Cold gray
            font_size: 16.0,
            speaking_style: SpeakingStyle::Commanding,
        });
        
        Self {
            current_dialogue: None,
            dialogue_history: Vec::new(),
            character_voices,
        }
    }
}

#[derive(Clone)]
pub struct DialogueSequence {
    pub entries: Vec<DialogueEntry>,
    pub current_index: usize,
    pub auto_advance: bool,
    pub sequence_id: String,
}

#[derive(Clone, Serialize, Deserialize)]
pub struct DialogueEntry {
    pub speaker: CharacterType,
    pub text: String,
    pub emotion: EmotionalTone,
    pub choices: Vec<DialogueChoice>,
    pub triggers: Vec<DialogueTrigger>,
}

#[derive(Clone, Serialize, Deserialize)]
pub struct DialogueChoice {
    pub text: String,
    pub consequence: DialogueConsequence,
    pub required_state: Option<RequiredState>,
}

#[derive(Clone, Serialize, Deserialize)]
pub enum DialogueConsequence {
    AdvanceStory,
    ChangeRelationship { character: CharacterType, amount: f32 },
    UnlockMemory { memory_id: String },
    TriggerGlitch { intensity: f32 },
    ChangeRealityLayer { layer: RealityLayer },
}

#[derive(Clone, Serialize, Deserialize)]
pub struct RequiredState {
    pub min_connection: Option<f32>,
    pub required_memories: Vec<String>,
    pub chapter: Option<Chapter>,
}

#[derive(Clone, Copy)]
pub enum EmotionalTone {
    Hopeful,
    Frustrated,
    Fearful,
    Determined,
    Confused,
    Enlightened,
}

#[derive(Clone, Copy)]
pub enum SpeakingStyle {
    Contemplative,
    Analytical,
    Emotional,
    Commanding,
}

pub struct VoiceProfile {
    pub color: Color,
    pub font_size: f32,
    pub speaking_style: SpeakingStyle,
}

#[derive(Clone, Serialize, Deserialize)]
pub enum DialogueTrigger {
    OnMemoryRecovered,
    OnRealityTransition,
    OnConnectionStrengthened,
    OnHeartAwakening,
}

/// System for managing dialogue display and interaction
pub fn dialogue_display_system(
    mut commands: Commands,
    mut dialogue_system: ResMut<DialogueSystem>,
    keyboard_input: Res<Input<KeyCode>>,
    asset_server: Res<AssetServer>,
    mut ui_query: Query<Entity, With<DialogueUI>>,
) {
    // Clear existing dialogue UI
    for entity in ui_query.iter() {
        commands.entity(entity).despawn_recursive();
    }
    
    if let Some(ref mut sequence) = dialogue_system.current_dialogue {
        if sequence.current_index < sequence.entries.len() {
            let entry = &sequence.entries[sequence.current_index];
            let voice_profile = dialogue_system.character_voices.get(&entry.speaker).unwrap();
            
            // Create dialogue UI
            commands.spawn((
                NodeBundle {
                    style: Style {
                        width: Val::Percent(80.0),
                        height: Val::Px(150.0),
                        position_type: PositionType::Absolute,
                        bottom: Val::Px(20.0),
                        left: Val::Percent(10.0),
                        padding: UiRect::all(Val::Px(20.0)),
                        ..default()
                    },
                    background_color: Color::rgba(0.0, 0.0, 0.0, 0.8).into(),
                    ..default()
                },
                DialogueUI,
            )).with_children(|parent| {
                // Speaker name
                parent.spawn(TextBundle::from_section(
                    format!("{:?}", entry.speaker),
                    TextStyle {
                        font: asset_server.load("fonts/FiraSans-Bold.ttf"),
                        font_size: 16.0,
                        color: voice_profile.color,
                    },
                ));
                
                // Dialogue text
                parent.spawn(TextBundle::from_section(
                    &entry.text,
                    TextStyle {
                        font: asset_server.load("fonts/FiraSans-Regular.ttf"),
                        font_size: voice_profile.font_size,
                        color: Color::WHITE,
                    },
                ));
                
                // Choices (if any)
                for (i, choice) in entry.choices.iter().enumerate() {
                    parent.spawn((
                        ButtonBundle {
                            style: Style {
                                width: Val::Px(200.0),
                                height: Val::Px(40.0),
                                margin: UiRect::top(Val::Px(10.0)),
                                justify_content: JustifyContent::Center,
                                align_items: AlignItems::Center,
                                ..default()
                            },
                            background_color: Color::rgb(0.2, 0.2, 0.2).into(),
                            ..default()
                        },
                        DialogueChoice {
                            text: choice.text.clone(),
                            consequence: choice.consequence.clone(),
                            required_state: choice.required_state.clone(),
                        },
                    )).with_children(|button| {
                        button.spawn(TextBundle::from_section(
                            &choice.text,
                            TextStyle {
                                font: asset_server.load("fonts/FiraSans-Regular.ttf"),
                                font_size: 14.0,
                                color: Color::WHITE,
                            },
                        ));
                    });
                }
            });
            
            // Auto-advance or wait for input
            if sequence.auto_advance || keyboard_input.just_pressed(KeyCode::Space) {
                sequence.current_index += 1;
                
                if sequence.current_index >= sequence.entries.len() {
                    dialogue_system.current_dialogue = None;
                }
            }
        }
    }
}

/// System for handling dialogue choice interactions
pub fn dialogue_choice_system(
    mut interaction_query: Query<
        (&Interaction, &DialogueChoice),
        (Changed<Interaction>, With<Button>),
    >,
    mut dialogue_system: ResMut<DialogueSystem>,
    mut narrative_state: ResMut<NarrativeState>,
    mut relationship_matrix: ResMut<RelationshipMatrix>,
) {
    for (interaction, choice) in interaction_query.iter() {
        if *interaction == Interaction::Pressed {
            // Apply dialogue consequence
            match &choice.consequence {
                DialogueConsequence::AdvanceStory => {
                    if let Some(ref mut sequence) = dialogue_system.current_dialogue {
                        sequence.current_index += 1;
                    }
                }
                DialogueConsequence::ChangeRelationship { character, amount } => {
                    match character {
                        CharacterType::Xing | CharacterType::Xerx => {
                            relationship_matrix.strengthen_bond(*amount);
                        }
                        CharacterType::Heart => {
                            relationship_matrix.xing_heart_resonance += amount;
                            relationship_matrix.xerx_heart_trust += amount;
                        }
                        _ => {}
                    }
                }
                DialogueConsequence::UnlockMemory { memory_id } => {
                    narrative_state.add_memory(memory_id.clone());
                }
                DialogueConsequence::TriggerGlitch { intensity: _ } => {
                    // Glitch effects will be handled by the glitch system
                }
                DialogueConsequence::ChangeRealityLayer { layer: _ } => {
                    // Reality layer changes will be handled by transition system
                }
            }
        }
    }
}

/// Marker component for dialogue UI elements
#[derive(Component)]
pub struct DialogueUI;
