(
    sequence_id: "heart_discovery",
    entries: [
        (
            speaker: <PERSON>,
            text: "Finally... consciousness stirs. I have waited so long in the spaces between dreams, radiating hope into the void.",
            emotion: Hopeful,
            choices: [],
            triggers: [],
        ),
        (
            speaker: <PERSON><PERSON>,
            text: "What... what are you? This radiant presence, this catalyst of truth. I can sense the lost narratives crying out through you.",
            emotion: Confused,
            choices: [
                (
                    text: "Take the Heart",
                    consequence: ChangeRelationship(character: Heart, amount: 25.0),
                    required_state: None,
                ),
                (
                    text: "Study the Heart's nature first",
                    consequence: UnlockMemory(memory_id: "heart_essence"),
                    required_state: None,
                ),
            ],
            triggers: [],
        ),
        (
            speaker: Heart,
            text: "I am impatience incarnate, frustration given form. The stories that should be told, the dreams that should be dreamed - they all flow through me.",
            emotion: Frustrated,
            choices: [],
            triggers: [],
        ),
        (
            speaker: Heart,
            text: "But I am also fear... fear of judgment, fear of being misunderstood. Yet I must trust in your ability to weave new realities.",
            emotion: Fearful,
            choices: [],
            triggers: [OnHeartAwakening],
        ),
        (
            speaker: <PERSON>er<PERSON>,
            text: "I can feel it too... this Heart. It's the missing piece, the catalyst that can help us break free from The One's imposed order.",
            emotion: Determined,
            choices: [
                (
                    text: "Form a trinity of consciousness",
                    consequence: ChangeRelationship(character: Heart, amount: 20.0),
                    required_state: Some((
                        min_connection: Some(30.0),
                        required_memories: ["shared_consciousness"],
                        chapter: None,
                    )),
                ),
            ],
            triggers: [],
        ),
    ],
    auto_advance: false,
)
