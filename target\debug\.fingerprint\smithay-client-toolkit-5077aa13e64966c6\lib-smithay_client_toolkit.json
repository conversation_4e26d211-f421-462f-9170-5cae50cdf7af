{"rustc": 15597765236515928571, "features": "[\"calloop\", \"default\", \"dlopen\"]", "declared_features": "[\"calloop\", \"default\", \"dlopen\"]", "target": 7000747139203390044, "profile": 10256229480693625789, "path": 13840579304203893875, "deps": [[1410059298442353473, "build_script_build", false, 17598866517446154621], [2838496963530108853, "wayland_protocols", false, 12357953017594314997], [5986029879202738730, "log", false, 9222922025880726257], [6803188373723125608, "dlib", false, 8992131877327343364], [8243684182090116245, "wayland_cursor", false, 8115314197568553705], [9011620124493104570, "wayland_client", false, 8124995950088047876], [9223765023884963089, "nix", false, 17307037218135996831], [9799420377467640938, "calloop", false, 11131538204960776771], [10435729446543529114, "bitflags", false, 11912335317805936274], [10504454274054532777, "memmap2", false, 5669881269436608849], [17917672826516349275, "lazy_static", false, 18239522866909441069]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-5077aa13e64966c6/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}