{"rustc": 15597765236515928571, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 8285369720897779924, "path": 1392751382480501338, "deps": [[1988483478007900009, "unicode_ident", false, 9685569166477003960], [3060637413840920116, "proc_macro2", false, 8402778786256717417], [17990358020177143287, "quote", false, 9609691878019640331]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-10e2fb3c3accea5b/dep-lib-syn", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}