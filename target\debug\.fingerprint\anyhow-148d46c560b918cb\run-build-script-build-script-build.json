{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 323669389985880942]], "local": [{"RerunIfChanged": {"output": "debug/build/anyhow-148d46c560b918cb/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}