{"rustc": 15597765236515928571, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 10256229480693625789, "path": 13460280506177051175, "deps": [[555019317135488525, "regex_automata", false, 10264473463568351204], [2779309023524819297, "aho_corasick", false, 10958130930386920235], [9408802513701742484, "regex_syntax", false, 1384400382503748789], [15932120279885307830, "memchr", false, 14873508960946443447]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-661eb41226cfad7c/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}