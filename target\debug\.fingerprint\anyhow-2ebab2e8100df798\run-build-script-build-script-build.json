{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 14108226417058111747]], "local": [{"RerunIfChanged": {"output": "debug/build/anyhow-2ebab2e8100df798/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 0, "compile_kind": 0}