{"rustc": 15597765236515928571, "features": "[\"wayland\", \"x11\"]", "declared_features": "[\"accesskit_unix\", \"trace\", \"wayland\", \"x11\"]", "target": 10908655140656848409, "profile": 10256229480693625789, "path": 10396597941564831094, "deps": [[597746441010298004, "bevy_derive", false, 14738724730754875882], [923331430220980291, "bevy_tasks", false, 15773927132577434283], [1067171486353553040, "bevy_ecs", false, 4483169035599705488], [2768765871680453703, "bevy_app", false, 5722803705385748630], [4217114486562132249, "bevy_input", false, 10539950649205974228], [4861025433250552643, "bevy_window", false, 13014080302200445698], [5516665811914208555, "bevy_hierarchy", false, 4332301928768071157], [6812015408794860941, "bevy_math", false, 13369206409795160803], [8275439307549159653, "bevy_a11y", false, 17178603615110920987], [8874671706155897836, "bevy_utils", false, 14158135305627284877], [10036841630170532596, "winit", false, 7162149633860407712], [11693073011723388840, "raw_window_handle", false, 11961575583575117315], [15677050387741058262, "approx", false, 4261803187203164938], [16009455726594798983, "accesskit_winit", false, 13593794437007715460]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_winit-3ae942408afb20ec/dep-lib-bevy_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}