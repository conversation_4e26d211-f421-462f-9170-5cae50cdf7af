{"rustc": 15597765236515928571, "features": "[\"clone\", \"codespan-reporting\", \"default\", \"glsl-in\", \"glsl-out\", \"hexf-parse\", \"pp-rs\", \"span\", \"spirv\", \"spv-out\", \"termcolor\", \"unicode-xid\", \"validate\", \"wgsl-in\", \"wgsl-out\"]", "declared_features": "[\"arbitrary\", \"clone\", \"codespan-reporting\", \"default\", \"deserialize\", \"dot-out\", \"glsl-in\", \"glsl-out\", \"hexf-parse\", \"hlsl-out\", \"msl-out\", \"petgraph\", \"pp-rs\", \"serde\", \"serialize\", \"span\", \"spirv\", \"spv-in\", \"spv-out\", \"termcolor\", \"unicode-xid\", \"validate\", \"wgsl-in\", \"wgsl-out\"]", "target": 10316217455984245758, "profile": 10256229480693625789, "path": 11813942474654543883, "deps": [[4206236867992986649, "bit_set", false, 3181901266723483877], [5157631553186200874, "num_traits", false, 6039425563362279220], [5986029879202738730, "log", false, 9222922025880726257], [6511967590362104379, "codespan_reporting", false, 4879333939935233593], [7896293946984509699, "bitflags", false, 3051709552260478752], [8008191657135824715, "thiserror", false, 1414080933147118704], [11064059710780544854, "spirv", false, 1538453708860275502], [11741667666137467643, "hexf_parse", false, 13110569758927634700], [12902659978838094914, "termcolor", false, 6448838455067728784], [14923790796823607459, "indexmap", false, 3063100210383541682], [16055916053474393816, "rustc_hash", false, 15502100922533563675], [16126285161989458480, "unicode_xid", false, 4119119031227132342], [17978653510626507258, "pp_rs", false, 14863238915156236286]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/naga-ab6ac2055eb8fae0/dep-lib-naga", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}