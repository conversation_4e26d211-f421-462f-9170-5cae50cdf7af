{"rustc": 15597765236515928571, "features": "[\"bevy_reflect\", \"default\"]", "declared_features": "[\"bevy_reflect\", \"default\", \"multi-threaded\", \"trace\"]", "target": 2920551851862429248, "profile": 10256229480693625789, "path": 11137621405740212539, "deps": [[923331430220980291, "bevy_tasks", false, 15773927132577434283], [1359731229228270592, "thread_local", false, 9986603091177237913], [1464803193346256239, "event_listener", false, 6392904338672160139], [1720748814177985408, "bevy_reflect", false, 4562617993401875491], [5302544599749092241, "async_channel", false, 8730797983012696193], [6412249515098474706, "bevy_ecs_macros", false, 14248750441351864488], [7813745145863863629, "bevy_ptr", false, 2656224939316987465], [8008191657135824715, "thiserror", false, 1414080933147118704], [8874671706155897836, "bevy_utils", false, 14158135305627284877], [9689903380558560274, "serde", false, 11908065706694279725], [11434239582363224126, "downcast_rs", false, 10789207993170213021], [16055916053474393816, "rustc_hash", false, 15502100922533563675], [18312645897321731715, "fixedbitset", false, 16186977505417465962]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_ecs-e0228d0927fdca67/dep-lib-bevy_ecs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}