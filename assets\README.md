# Assets Directory

This directory contains all game assets organized for optimal loading performance.

## Directory Structure

```
assets/
├── sprites/          # Sprite images and textures
│   ├── player.png
│   ├── enemy.png
│   ├── background.png
│   └── ui/
├── audio/           # Sound effects and music
│   ├── music/
│   └── sfx/
├── fonts/           # Font files
└── data/            # Game data files (JSON, RON)
```

## Asset Optimization Guidelines

1. **Images**: Use PNG for sprites with transparency, JPEG for backgrounds
2. **Audio**: Use OGG Vorbis for music, WAV for short sound effects
3. **Fonts**: Use TTF or OTF formats
4. **Data**: Use RON format for configuration files

## Performance Tips

- Keep texture sizes power-of-2 when possible
- Use texture atlases for small sprites
- Compress audio files appropriately
- Preload frequently used assets
