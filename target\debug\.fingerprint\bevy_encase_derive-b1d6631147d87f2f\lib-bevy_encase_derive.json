{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[]", "target": 14161042395232763202, "profile": 8285369720897779924, "path": 7626919419969566574, "deps": [[8081174564369101661, "encase_derive_impl", false, 2715348044984108560], [13800953573317674826, "bevy_macro_utils", false, 12046363352588924291]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_encase_derive-b1d6631147d87f2f/dep-lib-bevy_encase_derive", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}