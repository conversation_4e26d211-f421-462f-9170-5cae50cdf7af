{"rustc": 15597765236515928571, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 6661914849467474781, "path": 10707664996105353335, "deps": [[189982446159473706, "parking", false, 6271075982803974123], [1906322745568073236, "pin_project_lite", false, 6347187501733791113], [12100481297174703255, "concurrent_queue", false, 5360316459395033506]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/event-listener-8cb4a7dc713fcf3f/dep-lib-event_listener", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}