/mnt/f/Epoch of Elria/target/debug/deps/libwayland_protocols-abf80c2149a70498.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-activation-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-unstable-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/server-decoration_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/viewporter_client_api.rs

/mnt/f/Epoch of Elria/target/debug/deps/libwayland_protocols-abf80c2149a70498.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-activation-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-unstable-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/server-decoration_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/viewporter_client_api.rs

/mnt/f/Epoch of Elria/target/debug/deps/wayland_protocols-abf80c2149a70498.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-activation-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/fullscreen-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/idle-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-timestamps-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-explicit-synchronization-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-constraints-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-gestures-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/primary-selection-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/relative-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v3_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-decoration-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-output-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v5_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v6_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/gtk-primary-selection_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-unstable-v2_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/server-decoration_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-data-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-export-dmabuf-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-gamma-control-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-input-inhibitor-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-layer-shell-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-power-management-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-screencopy-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-virtual-pointer-v1_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/presentation-time_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell_client_api.rs /mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/viewporter_client_api.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/protocol_macro.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/staging.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/unstable.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/misc.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/wlr.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.29.5/src/stable.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-activation-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/fullscreen-shell-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/idle-inhibit-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-timestamps-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/keyboard-shortcuts-inhibit-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-dmabuf-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/linux-explicit-synchronization-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-constraints-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/pointer-gestures-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/primary-selection-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/relative-pointer-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/tablet-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/text-input-v3_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-decoration-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-foreign-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-output-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v5_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell-v6_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xwayland-keyboard-grab-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/gtk-primary-selection_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/input-method-unstable-v2_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/server-decoration_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-data-control-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-export-dmabuf-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-foreign-toplevel-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-gamma-control-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-input-inhibitor-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-layer-shell-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-output-power-management-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-screencopy-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/wlr-virtual-pointer-v1_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/presentation-time_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/xdg-shell_client_api.rs:
/mnt/f/Epoch\ of\ Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out/viewporter_client_api.rs:

# env-dep:OUT_DIR=/mnt/f/Epoch of Elria/target/debug/build/wayland-protocols-44e8cbd89d2a5b98/out
