{"rustc": 15597765236515928571, "features": "[\"bevy\", \"bevy_math\", \"default\", \"glam\", \"smallvec\", \"smol_str\"]", "declared_features": "[\"bevy\", \"bevy_math\", \"default\", \"documentation\", \"glam\", \"smallvec\", \"smol_str\"]", "target": 10834165306654912026, "profile": 10256229480693625789, "path": 12103533790352586325, "deps": [[3571374251074753029, "smol_str", false, 3421984263112721291], [3666196340704888985, "smallvec", false, 11275249618712392638], [4201017002668312204, "glam", false, 8351838409691551647], [6812015408794860941, "bevy_math", false, 13369206409795160803], [7071162725505148458, "bevy_reflect_derive", false, 8289768378188120817], [7813745145863863629, "bevy_ptr", false, 2656224939316987465], [8008191657135824715, "thiserror", false, 1414080933147118704], [8443559281687440230, "erased_serde", false, 2490286564296132331], [8874671706155897836, "bevy_utils", false, 14158135305627284877], [9689903380558560274, "serde", false, 11908065706694279725], [11434239582363224126, "downcast_rs", false, 10789207993170213021]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bevy_reflect-c3b5635ddd5b413c/dep-lib-bevy_reflect", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}