{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 10256229480693625789, "path": 4031139206100936657, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/profiling-051b20867307d3cf/dep-lib-profiling", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=gold"], "config": 2069994364910194474, "compile_kind": 0}