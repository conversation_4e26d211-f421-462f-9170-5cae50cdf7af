{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 6942269584725465202, "path": 18369052694101749293, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\profiling-615a09735cd51c92\\dep-lib-profiling", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}