{"rustc": 15597765236515928571, "features": "[\"aarch64_simd\", \"bytemuck_derive\", \"derive\", \"extern_crate_alloc\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"impl_core_error\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\", \"zeroable_unwind_fn\"]", "target": 5195934831136530909, "profile": 9014799531105028339, "path": 7030301833342605741, "deps": [[15246557919602675095, "bytemuck_derive", false, 3268597014194969928]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bytemuck-a0747168ac679e59/dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}