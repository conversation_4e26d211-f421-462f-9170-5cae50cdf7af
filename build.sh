#!/bin/bash

# Build script for The Dream Weaver's Heart - Epoch of Elria
# Optimized Rust game build automation for Linux/WSL

echo "🎮 Building The Dream Weaver's Heart - Epoch of Elria"
echo "================================================="

# Check if Rust is installed and up to date
if command -v cargo &> /dev/null; then
    echo "✅ Rust found: $(cargo --version)"
else
    echo "❌ Rust not found. Please install Rust first:"
    echo "   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

# Install additional components if not present
echo "🔧 Installing additional Rust components..."
rustup component add clippy rustfmt

# Install required system dependencies for Bevy (graphics/audio)
echo "📦 Checking system dependencies..."
if command -v apt &> /dev/null; then
    echo "Installing required system packages for Bevy..."
    sudo apt update
    sudo apt install -y \
        pkg-config \
        libx11-dev \
        libasound2-dev \
        libudev-dev \
        libxkbcommon-x11-0 \
        libwayland-dev \
        libxrandr-dev \
        libxi-dev
fi

# Build the game
echo "🏗️  Building the game..."

# Development build (faster compilation)
echo "Building development version..."
if cargo build; then
    echo "✅ Development build successful!"
    
    # Ask if user wants to build release version
    read -p "Build optimized release version? (y/N): " build_release
    if [[ $build_release =~ ^[Yy]$ ]]; then
        echo "🚀 Building optimized release version..."
        if cargo build --release; then
            echo "✅ Release build successful!"
        else
            echo "❌ Release build failed!"
        fi
    fi
    
    # Ask if user wants to run the game
    read -p "Run the game now? (Y/n): " run_game
    if [[ ! $run_game =~ ^[Nn]$ ]]; then
        echo "🎮 Starting The Dream Weaver's Heart..."
        
        # Check if we're in WSL and need to handle graphics
        if grep -qi microsoft /proc/version; then
            echo "🖥️  Detected WSL environment"
            echo "Note: You may need to set up X11 forwarding or use WSLg for graphics"
            echo "For WSLg (Windows 11): Graphics should work automatically"
            echo "For X11 forwarding: Make sure you have an X server running on Windows"
        fi
        
        cargo run
    fi
    
else
    echo "❌ Build failed! Check the error messages above."
    echo "💡 Common solutions:"
    echo "   - Make sure all system dependencies are installed"
    echo "   - Check that you have the latest Rust version: rustup update"
    echo "   - Try: cargo clean && cargo build"
    echo "   - For WSL graphics issues, ensure WSLg is enabled or X11 forwarding is set up"
fi

echo ""
echo "🎭 The Dream Weaver's Heart - Ready to explore the narrative!"
echo "================================================="

# Additional helpful commands
echo "📝 Useful commands:"
echo "   cargo run                    # Run the game"
echo "   cargo build --release       # Build optimized version"
echo "   cargo test                   # Run tests"
echo "   cargo clippy                 # Lint code"
echo "   cargo fmt                    # Format code"
