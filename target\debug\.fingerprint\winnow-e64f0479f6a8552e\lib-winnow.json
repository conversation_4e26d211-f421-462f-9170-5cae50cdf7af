{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 14113089254465536004, "profile": 8285369720897779924, "path": 4385819912966196564, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-e64f0479f6a8552e/dep-lib-winnow", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=lld"], "config": 2069994364910194474, "compile_kind": 0}