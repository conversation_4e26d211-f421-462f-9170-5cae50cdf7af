# Cargo configuration for optimized builds

[build]
# Use default linker for better WSL compatibility
# rustflags = ["-C", "link-arg=-fuse-ld=lld"]

# Target-specific configurations
[target.x86_64-pc-windows-msvc]
rustflags = ["-C", "link-arg=/SUBSYSTEM:WINDOWS"]

[target.x86_64-pc-windows-gnu]
rustflags = ["-C", "link-arg=-Wl,--subsystem,windows"]

# Faster builds in development
[profile.dev]
debug = 1
incremental = true

# Unstable features for faster compilation (requires nightly)
# [unstable]
# build-std = ["std", "panic_abort"]
# build-std-features = ["panic_immediate_abort"]
