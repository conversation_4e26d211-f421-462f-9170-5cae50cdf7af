use bevy::prelude::*;
use crate::components::*;
use crate::systems::*;
use crate::resources::*;
use crate::states::*;

/// Main game plugin that orchestrates all game systems
pub struct GamePlugin;

impl Plugin for GamePlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Update, (
                transition_to_playing,
                handle_pause,
            ))
            .add_systems(Update, animate_sprites)
            .add_systems(Update, collision_system.run_if(in_state(GameState::Playing)));
    }
}

/// Player-related systems plugin
pub struct PlayerPlugin;

impl Plugin for PlayerPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Startup, spawn_player)
            .add_systems(Update, (
                handle_input,
                update_player,
            ).run_if(in_state(GameState::Playing)));
    }
}

/// World and environment systems plugin
pub struct WorldPlugin;

impl Plugin for WorldPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Startup, setup_world_environment)
            .add_systems(Update, (
                update_world,
                spawn_enemies,
                cleanup_entities,
            ).run_if(in_state(GameState::Playing)));
    }
}

/// UI systems plugin
pub struct UIPlugin;

impl Plugin for UIPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Startup, setup_ui)
            .add_systems(Update, update_ui.run_if(in_state(GameState::Playing)))
            .add_systems(Update, menu_system.run_if(in_state(GameState::Menu)));
    }
}

// Plugin implementation functions

fn spawn_player(mut commands: Commands, asset_server: Res<AssetServer>) {
    commands.spawn((
        SpriteBundle {
            texture: asset_server.load("sprites/player.png"),
            transform: Transform::from_xyz(0.0, 0.0, 1.0),
            ..default()
        },
        Player::default(),
        Velocity::zero(),
        Health::new(100.0),
    ));
}

fn setup_world_environment(mut commands: Commands, asset_server: Res<AssetServer>) {
    // Spawn background
    commands.spawn(SpriteBundle {
        texture: asset_server.load("sprites/background.png"),
        transform: Transform::from_xyz(0.0, 0.0, -1.0),
        ..default()
    });
    
    info!("World environment setup complete");
}

fn setup_ui(mut commands: Commands, asset_server: Res<AssetServer>) {
    // Health bar background
    commands.spawn((
        NodeBundle {
            style: Style {
                width: Val::Px(200.0),
                height: Val::Px(20.0),
                position_type: PositionType::Absolute,
                left: Val::Px(10.0),
                top: Val::Px(10.0),
                ..default()
            },
            background_color: Color::rgb(0.2, 0.2, 0.2).into(),
            ..default()
        },
    ));
    
    // Health bar fill
    commands.spawn((
        NodeBundle {
            style: Style {
                width: Val::Percent(100.0),
                height: Val::Percent(100.0),
                position_type: PositionType::Absolute,
                ..default()
            },
            background_color: Color::rgb(0.8, 0.2, 0.2).into(),
            ..default()
        },
        HealthBar,
    ));
    
    // Score text
    commands.spawn((
        TextBundle::from_section(
            "Score: 0",
            TextStyle {
                font: asset_server.load("fonts/FiraSans-Bold.ttf"),
                font_size: 24.0,
                color: Color::WHITE,
            },
        )
        .with_style(Style {
            position_type: PositionType::Absolute,
            right: Val::Px(10.0),
            top: Val::Px(10.0),
            ..default()
        }),
        ScoreText,
    ));
}

fn spawn_enemies(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
    time: Res<Time>,
    mut spawn_timer: Local<Timer>,
) {
    spawn_timer.tick(time.delta());
    
    if spawn_timer.just_finished() {
        // Reset timer for next spawn
        *spawn_timer = Timer::from_seconds(2.0, TimerMode::Once);
        
        // Spawn enemy at random position
        let x = (rand::random::<f32>() - 0.5) * 1200.0;
        let y = (rand::random::<f32>() - 0.5) * 600.0;
        
        commands.spawn((
            SpriteBundle {
                texture: asset_server.load("sprites/enemy.png"),
                transform: Transform::from_xyz(x, y, 1.0),
                ..default()
            },
            Enemy {
                damage: 10.0,
                attack_range: 50.0,
            },
            Velocity::new(
                (rand::random::<f32>() - 0.5) * 100.0,
                (rand::random::<f32>() - 0.5) * 100.0,
            ),
            Health::new(50.0),
        ));
    }
}

fn cleanup_entities(
    mut commands: Commands,
    query: Query<(Entity, &Health)>,
) {
    for (entity, health) in query.iter() {
        if !health.is_alive() {
            commands.entity(entity).despawn();
        }
    }
}
