use bevy::prelude::*;
use crate::components::*;
use crate::systems::*;
use crate::resources::*;
use crate::states::*;
use crate::narrative_systems::*;
use crate::dialogue_system::*;

/// Main game plugin that orchestrates all game systems
pub struct GamePlugin;

impl Plugin for GamePlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Update, (
                transition_to_playing,
                handle_pause,
            ))
            .add_systems(Update, animate_sprites)
            .add_systems(Update, collision_system.run_if(in_state(GameState::Playing)));
    }
}

/// Player-related systems plugin
pub struct PlayerPlugin;

impl Plugin for PlayerPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Startup, spawn_player)
            .add_systems(Update, (
                handle_input,
                update_player,
            ).run_if(in_state(GameState::Playing)));
    }
}

/// World and environment systems plugin
pub struct WorldPlugin;

impl Plugin for WorldPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Startup, setup_world_environment)
            .add_systems(Update, (
                update_world,
                spawn_enemies,
                cleanup_entities,
            ).run_if(in_state(GameState::Playing)));
    }
}

/// Narrative systems plugin for story management
pub struct NarrativePlugin;

impl Plugin for NarrativePlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Update, (
                narrative_weaving_system,
                memory_system,
                the_one_influence_system,
            ))
            .add_systems(Update, reality_transition_system)
            .add_systems(Update, glitch_system);
    }
}

/// Character-specific systems plugin
pub struct CharacterPlugin;

impl Plugin for CharacterPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Startup, (
                spawn_xing,
                spawn_xerx,
                spawn_heart,
            ))
            .add_systems(Update, character_interaction_system)
            .add_systems(Update, emotional_state_system);
    }
}

/// Dialogue and narrative delivery plugin
pub struct DialoguePlugin;

impl Plugin for DialoguePlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Update, (
                dialogue_display_system,
                dialogue_choice_system,
            ))
            .add_systems(Startup, load_story_dialogues);
    }
}

/// Reality layer management plugin
pub struct RealityPlugin;

impl Plugin for RealityPlugin {
    fn build(&self, app: &mut App) {
        app
            .add_systems(Update, reality_layer_visual_system)
            .add_systems(Update, reality_stability_system)
            .add_systems(OnEnter(RealityState::Transitioning), handle_reality_transition)
            .add_systems(OnExit(RealityState::Glitching), cleanup_glitch_effects);
    }
}

// Story-specific character spawning functions

fn spawn_xing(mut commands: Commands, asset_server: Res<AssetServer>) {
    commands.spawn((
        SpriteBundle {
            texture: asset_server.load("sprites/xing_weaver.png"),
            transform: Transform::from_xyz(-200.0, 0.0, 1.0),
            ..default()
        },
        Character::xing(),
        RealityLayer::InfiniteLibrary,
        WeavingAbility {
            pattern_complexity: 75.0,
            reality_influence: 60.0,
            active_threads: Vec::new(),
        },
        EmotionalState::balanced(),
    ));
}

fn spawn_xerx(mut commands: Commands, asset_server: Res<AssetServer>) {
    commands.spawn((
        SpriteBundle {
            texture: asset_server.load("sprites/xerx_architect.png"),
            transform: Transform::from_xyz(200.0, 0.0, 1.0),
            ..default()
        },
        Character::xerx(),
        RealityLayer::SterileReality,
        ArchitecturalPower {
            structural_integrity: 85.0,
            design_precision: 90.0,
            memory_vault_access: true,
        },
        EmotionalState::balanced(),
    ));
}

fn spawn_heart(mut commands: Commands, asset_server: Res<AssetServer>) {
    commands.spawn((
        SpriteBundle {
            texture: asset_server.load("sprites/heart_catalyst.png"),
            transform: Transform::from_xyz(0.0, 100.0, 2.0),
            sprite: Sprite {
                color: Color::rgba(1.0, 0.6, 0.8, 0.9),
                ..default()
            },
            ..default()
        },
        Character::the_heart(),
        RealityLayer::DreamRealm,
        EmotionalState::heart_state(),
    ));
}

fn setup_world_environment(mut commands: Commands, asset_server: Res<AssetServer>) {
    // Spawn background
    commands.spawn(SpriteBundle {
        texture: asset_server.load("sprites/background.png"),
        transform: Transform::from_xyz(0.0, 0.0, -1.0),
        ..default()
    });
    
    info!("World environment setup complete");
}

fn setup_ui(mut commands: Commands, asset_server: Res<AssetServer>) {
    // Health bar background
    commands.spawn((
        NodeBundle {
            style: Style {
                width: Val::Px(200.0),
                height: Val::Px(20.0),
                position_type: PositionType::Absolute,
                left: Val::Px(10.0),
                top: Val::Px(10.0),
                ..default()
            },
            background_color: Color::rgb(0.2, 0.2, 0.2).into(),
            ..default()
        },
    ));
    
    // Health bar fill
    commands.spawn((
        NodeBundle {
            style: Style {
                width: Val::Percent(100.0),
                height: Val::Percent(100.0),
                position_type: PositionType::Absolute,
                ..default()
            },
            background_color: Color::rgb(0.8, 0.2, 0.2).into(),
            ..default()
        },
        HealthBar,
    ));
    
    // Score text
    commands.spawn((
        TextBundle::from_section(
            "Score: 0",
            TextStyle {
                font: asset_server.load("fonts/FiraSans-Bold.ttf"),
                font_size: 24.0,
                color: Color::WHITE,
            },
        )
        .with_style(Style {
            position_type: PositionType::Absolute,
            right: Val::Px(10.0),
            top: Val::Px(10.0),
            ..default()
        }),
        ScoreText,
    ));
}

fn spawn_enemies(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
    time: Res<Time>,
    mut spawn_timer: Local<Timer>,
) {
    spawn_timer.tick(time.delta());
    
    if spawn_timer.just_finished() {
        // Reset timer for next spawn
        *spawn_timer = Timer::from_seconds(2.0, TimerMode::Once);
        
        // Spawn enemy at random position
        let x = (rand::random::<f32>() - 0.5) * 1200.0;
        let y = (rand::random::<f32>() - 0.5) * 600.0;
        
        commands.spawn((
            SpriteBundle {
                texture: asset_server.load("sprites/enemy.png"),
                transform: Transform::from_xyz(x, y, 1.0),
                ..default()
            },
            Enemy {
                damage: 10.0,
                attack_range: 50.0,
            },
            Velocity::new(
                (rand::random::<f32>() - 0.5) * 100.0,
                (rand::random::<f32>() - 0.5) * 100.0,
            ),
            Health::new(50.0),
        ));
    }
}

fn cleanup_entities(
    mut commands: Commands,
    query: Query<(Entity, &Health)>,
) {
    for (entity, health) in query.iter() {
        if !health.is_alive() {
            commands.entity(entity).despawn();
        }
    }
}
